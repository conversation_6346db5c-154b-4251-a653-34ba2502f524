{"version": 3, "file": "server-config.mjs", "sources": ["../../src/mastra/index.ts"], "sourcesContent": ["import { <PERSON><PERSON>, Agent } from \"@mastra/core\"\nimport { createAnthropic } from \"@ai-sdk/anthropic\"\n\n/**\n * Create a simple Company Researcher agent for Studio\n */\nfunction createCompanyResearcherAgent() {\n  console.log(\"🔍 Creating Company Researcher agent...\")\n\n  try {\n    // Create LLM client for Anthropic\n    const anthropic = createAnthropic({\n      apiKey: process.env.ANTHROPIC_API_KEY,\n    })\n\n    const agent = new Agent({\n      name: \"Company Researcher\",\n      instructions: `You are an expert corporate research analyst. Provide comprehensive company analysis including business model, financial performance, market position, and growth opportunities. Be thorough and professional.`,\n      model: anthropic(\"claude-3-5-sonnet-20241022\"),\n    })\n\n    console.log(\"✅ Created Company Researcher agent\")\n    return agent\n  } catch (error) {\n    console.log(\n      `⚠️  Failed to create Company Researcher: ${error instanceof Error ? error.message : \"unknown error\"}`,\n    )\n    return null\n  }\n}\n\n// Create the agent\nconst companyResearcher = createCompanyResearcherAgent()\n\n// Create Mastra instance\nexport const mastra = new Mastra({\n  agents: companyResearcher ? { companyresearcher: companyResearcher } : {},\n})\n\nconsole.log(\"✨ Mastra configuration complete!\")\nconsole.log(\"🤖 Agents available:\", Object.keys(mastra.agents || {}))\n"], "names": [], "mappings": "AAMA,MAAS,SAAA;;;;"}