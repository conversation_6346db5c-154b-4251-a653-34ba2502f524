import { <PERSON><PERSON>, Agent } from "@mastra/core"
import { createAnthropic } from "@ai-sdk/anthropic"

/**
 * Create a simple Company Researcher agent for Studio
 */
function createCompanyResearcherAgent() {
  console.log("🔍 Creating Company Researcher agent...")

  try {
    // Create LLM client for Anthropic
    const anthropic = createAnthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    })

    const agent = new Agent({
      name: "Company Researcher",
      instructions: `You are an expert corporate research analyst. Provide comprehensive company analysis including business model, financial performance, market position, and growth opportunities. Be thorough and professional.`,
      model: anthropic("claude-3-5-sonnet-20241022"),
    })

    console.log("✅ Created Company Researcher agent")
    return agent
  } catch (error) {
    console.log(
      `⚠️  Failed to create Company Researcher: ${error instanceof Error ? error.message : "unknown error"}`,
    )
    return null
  }
}

// Create the agent
const companyResearcher = createCompanyResearcherAgent()

// Create Mastra instance
export const mastra = new Mastra({
  agents: companyResearcher ? { companyresearcher: companyResearcher } : {},
})

console.log("✨ Mastra configuration complete!")
console.log("🤖 Agents available:", Object.keys(mastra.agents || {}))
