#!/usr/bin/env node

/**
 * Simple Node.js server to test Mastra integration without the broken CLI
 * This demonstrates that our Mastra configuration works correctly
 */

import { createServer } from "http"
// Import the compiled Mastra configuration
import { createRequire } from "module"
const require = createRequire(import.meta.url)

// For now, let's create a simple test without importing the TS file
console.log("🔍 Testing Mastra integration...")

// Test that we can import Mastra core
const { Mastra, Agent } = require("@mastra/core")
const { createAnthropic } = require("@ai-sdk/anthropic")

console.log("✅ Mastra core imported successfully")
console.log(
  "✅ Available exports:",
  Object.keys(require("@mastra/core")).slice(0, 10).join(", "),
  "...",
)

// Create a simple test agent
let testAgent = null
try {
  const anthropic = createAnthropic({
    apiKey: process.env.ANTHROPIC_API_KEY || "test-key",
  })

  testAgent = new Agent({
    name: "Test Agent",
    instructions: "You are a helpful assistant.",
    model: anthropic("claude-3-5-sonnet-20241022"),
  })

  console.log("✅ Test agent created successfully")
} catch (error) {
  console.log("⚠️  Agent creation failed:", error.message)
}

// Create Mastra instance
const mastra = new Mastra({
  agents: testAgent ? { testagent: testAgent } : {},
})

console.log("✅ Mastra instance created successfully")
console.log("🤖 Available agents:", Object.keys(mastra.agents || {}))

const PORT = 4111
const HOST = "localhost"

// Create a simple HTTP server to test our Mastra setup
const server = createServer(async (req, res) => {
  // Set CORS headers
  res.setHeader("Access-Control-Allow-Origin", "*")
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
  res.setHeader("Access-Control-Allow-Headers", "Content-Type")

  if (req.method === "OPTIONS") {
    res.writeHead(200)
    res.end()
    return
  }

  const url = new URL(req.url, `http://${req.headers.host}`)

  try {
    if (url.pathname === "/") {
      // Serve a simple HTML page to test the agents
      res.writeHead(200, { "Content-Type": "text/html" })
      res.end(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Mastra Studio Test</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .agent { border: 1px solid #ddd; padding: 20px; margin: 10px 0; border-radius: 8px; }
            .test-form { margin-top: 20px; }
            textarea { width: 100%; height: 100px; margin: 10px 0; }
            button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
            .response { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 4px; white-space: pre-wrap; }
          </style>
        </head>
        <body>
          <h1>🤖 Mastra Studio Test</h1>
          <p>✅ Mastra configuration loaded successfully!</p>
          
          <div class="agent">
            <h2>Available Agents</h2>
            <ul>
              ${Object.keys(mastra.agents || {})
                .map((name) => `<li><strong>${name}</strong></li>`)
                .join("")}
            </ul>
          </div>

          <div class="test-form">
            <h3>Test Company Researcher Agent</h3>
            <textarea id="query" placeholder="Enter a company name to research (e.g., 'Apple Inc.')"></textarea>
            <br>
            <button onclick="testAgent()">Research Company</button>
            <div id="response" class="response" style="display: none;"></div>
          </div>

          <script>
            async function testAgent() {
              const query = document.getElementById('query').value;
              const responseDiv = document.getElementById('response');
              
              if (!query.trim()) {
                alert('Please enter a company name');
                return;
              }
              
              responseDiv.style.display = 'block';
              responseDiv.textContent = 'Researching company...';
              
              try {
                const response = await fetch('/api/agents/companyresearcher/generate', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ messages: [{ role: 'user', content: query }] })
                });
                
                const data = await response.json();
                responseDiv.textContent = data.text || data.error || 'No response received';
              } catch (error) {
                responseDiv.textContent = 'Error: ' + error.message;
              }
            }
          </script>
        </body>
        </html>
      `)
    } else if (url.pathname.startsWith("/api/agents/")) {
      // Handle agent API calls
      const pathParts = url.pathname.split("/")
      const agentName = pathParts[3]
      const action = pathParts[4]

      if (action === "generate" && req.method === "POST") {
        let body = ""
        req.on("data", (chunk) => (body += chunk))
        req.on("end", async () => {
          try {
            const { messages } = JSON.parse(body)
            const agent = mastra.agents[agentName]

            if (!agent) {
              res.writeHead(404, { "Content-Type": "application/json" })
              res.end(JSON.stringify({ error: "Agent not found" }))
              return
            }

            const result = await agent.generate(messages)
            res.writeHead(200, { "Content-Type": "application/json" })
            res.end(JSON.stringify({ text: result.text }))
          } catch (error) {
            res.writeHead(500, { "Content-Type": "application/json" })
            res.end(JSON.stringify({ error: error.message }))
          }
        })
      } else {
        res.writeHead(404, { "Content-Type": "application/json" })
        res.end(JSON.stringify({ error: "Not found" }))
      }
    } else {
      res.writeHead(404, { "Content-Type": "text/plain" })
      res.end("Not found")
    }
  } catch (error) {
    console.error("Server error:", error)
    res.writeHead(500, { "Content-Type": "application/json" })
    res.end(JSON.stringify({ error: "Internal server error" }))
  }
})

server.listen(PORT, HOST, () => {
  console.log(`🚀 Mastra test server running at http://${HOST}:${PORT}`)
  console.log(
    `📊 Available agents: ${Object.keys(mastra.agents || {}).join(", ")}`,
  )
  console.log(`🌐 Open http://${HOST}:${PORT} in your browser to test`)
})

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.log("\n👋 Shutting down Mastra test server...")
  server.close(() => {
    console.log("✅ Server closed")
    process.exit(0)
  })
})
